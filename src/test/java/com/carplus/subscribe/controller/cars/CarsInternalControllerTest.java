package com.carplus.subscribe.controller.cars;

import com.carplus.subscribe.App;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.enums.ETagModelEnum;
import com.carplus.subscribe.service.CarsService;
import com.carplus.subscribe.service.SubscribeLevelService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.transaction.annotation.Transactional;

import static carplus.common.response.CarPlusCode.API_USE_INCORRECT;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional
class CarsInternalControllerTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private SubscribeLevelService subscribeLevelService;
    @Autowired
    private CarsService carsService;

    private final String MEMBER_ID = "K2765";

    @Test
    void addCars_VirtualCarAndMfgYearNull() throws Exception {

        String plateNo = "RAA0999";
        String carNo = "1809990101";

        String addVirtualCarMfgYearNullRequest = "{\n" +
            "  \"plateNo\": \"" + plateNo + "\",\n" +
            "  \"carState\": \"NEW\",\n" +
            "  \"carModelCode\": \"S000P\",\n" +
            "  \"seat\": 5,\n" +
            "  \"fuelType\": \"petrol95\",\n" +
            "  \"energyType\": \"GASOLINE\",\n" +
            "  \"currentMileage\": 1,\n" +
            "  \"equipIds\": [\n" +
            "    23, 22, 2, 21, 15, 16\n" +
            "  ],\n" +
            "  \"locationStationCode\": \"233\",\n" +
            "  \"subscribeLevel\": 29,\n" +
            "  \"tagIds\": [\n" +
            "    3\n" +
            "  ],\n" +
            "  \"displacement\": 1997,\n" +
            "  \"mfgYear\": 2025,\n" +
            "  \"carNo\": \"" + carNo + "\",\n" +
            "  \"vatNo\": \"12345678\",\n" +
            "  \"prepWorkdays\": 10\n" +
            "}";

        String modifyLaunchedRequest = "{\n" +
            "  \"launched\": \"open\"\n" +
            "}";

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars")
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .contentType(MediaType.APPLICATION_JSON)
            .content(addVirtualCarMfgYearNullRequest);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"));

        requestBuilder = patch("/internal/subscribe/v1/car/{plateNo}/launched", plateNo)
            .contentType(MediaType.APPLICATION_JSON)
            .content(modifyLaunchedRequest);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"));

        requestBuilder = get("/internal/subscribe/cars/{plateNo}", plateNo);
        mockMvc.perform(requestBuilder)
            .andDo(print())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.plateNo").value(plateNo))
            .andExpect(jsonPath("$.data.images").isNotEmpty())
            .andExpect(jsonPath("$.data.images[0].year").isNumber())
            .andExpect(jsonPath("$.data.images[0].paths").isNotEmpty())
            .andExpect(jsonPath("$.data.prepWorkdays").value(10));

        requestBuilder = get("/common/subscribe/cars/carNo/{carNo}", carNo);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.carNo").value(carNo))
            .andExpect(jsonPath("$.data.plateNo").value(plateNo))
            .andExpect(jsonPath("$.data.images").isNotEmpty());

        requestBuilder = get("/common/subscribe/car")
            .param("orderBy", "MILEAGE")
            .param("skip", "0")
            .param("limit", "50")
            .param("sort", "ASC")
            .param("tagIds", "3");
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.page.list[*].plateNo", hasItem(plateNo)))
            .andExpect(jsonPath("$.data.page.list[*].carNo", hasItem(carNo)))
            .andExpect(jsonPath("$.data.page.list[?(@.plateNo == 'RAA0999' && @.carNo == '1809990101')].images").isNotEmpty());
    }

    @Test
    void addCars_Failure_InvalidPrepWorkdays() throws Exception {
        String plateNo = "RAA0998";
        String carNo = "1809990102";

        String invalidPrepWorkdaysRequest = "{\n" +
            "  \"plateNo\": \"" + plateNo + "\",\n" +
            "  \"carStat\": \"NEW\",\n" +
            "  \"carModelCode\": \"S000P\",\n" +
            "  \"seat\": 5,\n" +
            "  \"fuelType\": \"petrol95\",\n" +
            "  \"currentMileage\": 1,\n" +
            "  \"equipIds\": [23, 22, 2, 21, 15, 16],\n" +
            "  \"locationStationCode\": \"233\",\n" +
            "  \"subscribeLevel\": 29,\n" +
            "  \"tagIds\": [3],\n" +
            "  \"displacement\": 1997,\n" +
            "  \"mfgYear\": null,\n" +
            "  \"carNo\": \"" + carNo + "\",\n" +
            "  \"prepWorkdays\": 128\n" + // 傳入無效值
            "}";

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars")
            .contentType(MediaType.APPLICATION_JSON)
            .content(invalidPrepWorkdaysRequest);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.statusCode").value(API_USE_INCORRECT.getCode()))
            .andExpect(jsonPath("$.message").value(allOf(
                containsString("準備工作天數不可大於 127"),
                containsString("車籍統編不可為空"),
                containsString("訂閱類別不可為空"))))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    void addCars_Failure_NotVirtualCarAndMfgYearNull() throws Exception {

        String plateNo = "RAG-1999";
        String carNo = "1809990101";

        String addVirtualCarMfgYearNullRequest = "{\n" +
            "  \"plateNo\": \"" + plateNo + "\",\n" +
            "  \"carStat\": \"NEW\",\n" +
            "  \"carModelCode\": \"S000P\",\n" +
            "  \"seat\": 5,\n" +
            "  \"fuelType\": \"petrol95\",\n" +
            "  \"currentMileage\": 1,\n" +
            "  \"equipIds\": [\n" +
            "    23, 22, 2, 21, 15, 16\n" +
            "  ],\n" +
            "  \"locationStationCode\": \"233\",\n" +
            "  \"subscribeLevel\": 29,\n" +
            "  \"tagIds\": [\n" +
            "    3\n" +
            "  ],\n" +
            "  \"displacement\": 1997,\n" +
            "  \"mfgYear\": null,\n" +
            "  \"carNo\": \"" + carNo + "\"\n" +
            "}";

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars")
            .contentType(MediaType.APPLICATION_JSON)
            .content(addVirtualCarMfgYearNullRequest);
        mockMvc.perform(requestBuilder)
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.statusCode").value(API_USE_INCORRECT.getCode()))
            .andExpect(jsonPath("$.message").value(containsString("非虛擬車之出廠年份不可為空")))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    void checkCar() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = get("/internal/subscribe/cars/check/{plateNo}", "RCE-7558")
            .contentType(MediaType.APPLICATION_JSON);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.energyType").exists())
            .andExpect(jsonPath("$.data.displacement").exists())
            .andExpect(jsonPath("$.data.seat").exists())
            .andExpect(jsonPath("$.data.mfgYear").exists())
            .andExpect(jsonPath("$.data.mfgMonth").exists())
            .andExpect(jsonPath("$.data.currentMileage").exists())
            .andExpect(jsonPath("$.data.locationStationCode").exists());
    }

    @Test
    void checkCar_Failure_CarAlreadyInSubscribeSystem() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = get("/internal/subscribe/cars/check/{plateNo}", "RCZ-7297")
            .contentType(MediaType.APPLICATION_JSON);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value(CAR_ALREADY_IN_SUBSCRIBE_SYSTEM.getCode()))
            .andExpect(jsonPath("$.message").value(CAR_ALREADY_IN_SUBSCRIBE_SYSTEM.getMsg()))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    void checkCar_Failure_CrsCarNotFound() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = get("/internal/subscribe/cars/check/{plateNo}", "RAA9999")
            .contentType(MediaType.APPLICATION_JSON);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value(CRS_CAR_NOT_FOUND.getCode()))
            .andExpect(jsonPath("$.message").value(CRS_CAR_NOT_FOUND.getMsg()))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    void addSingleCars() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars/single")
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .contentType(MediaType.APPLICATION_JSON)
            .content("{\n" +
                "    \"plateNo\": \"RCE-7558\",\n" +
                "    \"currentMileage\": 103784,\n" +
                "    \"carModelCode\": \"S0002\",\n" +
                "    \"carState\": \"NEW\",\n" +
                "    \"subscribeLevel\": 1,\n" +
                "    \"launched\": \"accident\",\n" +
                "    \"isSealandLaunched\": false,\n" +
                "    \"tagIds\": [3],\n" +
                "    \"equipIds\": [3, 6, 9, 12, 15],\n" +
                "    \"gearType\": \"at\",\n" +
                "    \"colorDesc\": \"灰\",\n" +
                "    \"fuelType\": \"petrol95\",\n" +
                "    \"vatNo\": \"12345678\",\n" +
                "    \"displacement\": 1997,\n" +
                "    \"seat\": 5,\n" +
                "    \"mfgYear\": 2019,\n" +
                "    \"prepWorkdays\": 10,\n" +
                "    \"carType\": \"sedan\"\n" +
                "}");
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"));

        requestBuilder = get("/internal/subscribe/cars/RCE-7558")
            .contentType(MediaType.APPLICATION_JSON);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.carNo").isString())
            .andExpect(jsonPath("$.data.crsNo").doesNotExist())
            .andExpect(jsonPath("$.data.prepWorkdays").value(10));
    }

    @Test
    void addSingleCars_Failure_SubscribeLevelNotFound() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars/single")
            .contentType(MediaType.APPLICATION_JSON)
            .content("{\n" +
            "    \"plateNo\": \"RCE-7558\",\n" +
            "    \"currentMileage\": 103784,\n" +
            "    \"carModelCode\": \"S0002\",\n" +
            "    \"carState\": \"NEW\",\n" +
            "    \"subscribeLevel\": 9999,\n" +
            "    \"launched\": \"accident\",\n" +
            "    \"isSealandLaunched\": false,\n" +
            "    \"tagIds\": [4],\n" +
            "    \"equipIds\": [3, 6, 9, 12, 15],\n" +
            "    \"gearType\": \"at\",\n" +
            "    \"colorDesc\": \"灰\",\n" +
            "    \"fuelType\": \"petrol95\",\n" +
            "    \"carType\": \"sedan\"\n" +
            "}");
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value(SUBSCRIBE_LEVEL_NOT_FOUND.getCode()));
    }

    @Test
    void addSingleCars_Failure_HasMutualExclusiveTagIds() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars/single")
            .contentType(MediaType.APPLICATION_JSON)
            .content("{\n" +
            "    \"plateNo\": \"RCE-7558\",\n" +
            "    \"currentMileage\": 103784,\n" +
            "    \"carModelCode\": \"S0002\",\n" +
            "    \"carState\": \"NEW\",\n" +
            "    \"subscribeLevel\": 1,\n" +
            "    \"launched\": \"accident\",\n" +
            "    \"isSealandLaunched\": false,\n" +
            "    \"tagIds\": [3, 4],\n" +
            "    \"equipIds\": [3, 6, 9, 12, 15],\n" +
            "    \"gearType\": \"at\",\n" +
            "    \"colorDesc\": \"灰\",\n" +
            "    \"fuelType\": \"petrol95\",\n" +
            "    \"carType\": \"sedan\"\n" +
            "}");
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value(TAG_IDS_CONTAIN_MONTHLY_DISCOUNTED_AND_LEVEL_DISCOUNTED.getCode()));
    }

    @Test
    void addSingleCars_Failure_MissingCorrespondingDiscountLevel() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars/single")
            .contentType(MediaType.APPLICATION_JSON)
            .content("{\n" +
            "    \"plateNo\": \"RCE-7558\",\n" +
            "    \"currentMileage\": 103784,\n" +
            "    \"carModelCode\": \"S0002\",\n" +
            "    \"carState\": \"NEW\",\n" +
            "    \"subscribeLevel\": 1,\n" +
            "    \"launched\": \"accident\",\n" +
            "    \"isSealandLaunched\": false,\n" +
            "    \"tagIds\": [4],\n" +
            "    \"equipIds\": [3, 6, 9, 12, 15],\n" +
            "    \"gearType\": \"at\",\n" +
            "    \"colorDesc\": \"灰\",\n" +
            "    \"fuelType\": \"petrol95\",\n" +
            "    \"carType\": \"sedan\"\n" +
            "}");

        subscribeLevelService.checkSubscribeLevelExistence(1);

        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value(MISSING_CORRESPONDING_DISCOUNT_LEVEL.getCode()));
    }

    @Test
    void addSingleCars_FromCRS_Success_StdPriceNotNull() throws Exception {
        // 使用已存在於 CRS DB 但不存在於 Subscribe DB 的車牌號碼
        String plateNo = "GGG-8989";

        // 準備新增請求 - 使用 CRS 中的車輛資料
        String addSingleCarRequest = "{\n" +
            "    \"plateNo\": \"" + plateNo + "\",\n" +
            "    \"currentMileage\": 0,\n" +
            "    \"carModelCode\": \"S002G\",\n" +
            "    \"carState\": \"NEW\",\n" +
            "    \"subscribeLevel\": 1,\n" +
            "    \"launched\": \"open\",\n" +
            "    \"isSealandLaunched\": false,\n" +
            "    \"tagIds\": [3],\n" +
            "    \"equipIds\": [3, 6, 9, 12, 15],\n" +
            "    \"gearType\": \"at\",\n" +
            "    \"colorDesc\": \"紅色\",\n" +
            "    \"fuelType\": \"petrol95\",\n" +
            "    \"vatNo\": \"12208883\",\n" +
            "    \"displacement\": 1600,\n" +
            "    \"seat\": 5,\n" +
            "    \"mfgYear\": 2025,\n" +
            "    \"prepWorkdays\": 7,\n" +
            "    \"carType\": \"sedan\"\n" +
            "}";

        // 執行新增請求
        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars/single")
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .contentType(MediaType.APPLICATION_JSON)
            .content(addSingleCarRequest);

        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 驗證車輛已成功新增到 Subscribe DB 且 stdPrice 不為 null
        requestBuilder = get("/internal/subscribe/cars/{plateNo}", plateNo)
            .contentType(MediaType.APPLICATION_JSON);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.plateNo").value(plateNo))
            .andExpect(jsonPath("$.data.stdPrice").isNumber())
            .andExpect(jsonPath("$.data.stdPrice").value(not(nullValue())))
            .andExpect(jsonPath("$.data.carModel.carModelCode").value("S002G"))
            .andExpect(jsonPath("$.data.colorDesc").value("紅色"))
            .andExpect(jsonPath("$.data.prepWorkdays").value(7));

        // 進一步驗證 stdPrice 的值應該來自 CRS 的 stdPrice (10000000)
        Cars addedCar = carsService.findByPlateNo(plateNo);
        assertNotNull(addedCar);
        assertNotNull(addedCar.getStdPrice());
        assertEquals(10000000, addedCar.getStdPrice());
    }

    @Test
    void getCarInfo_Success_WithStatusNameAndPurposeCodeName() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = get("/internal/subscribe/cars/{plateNo}", "RBX-2073")
            .contentType(MediaType.APPLICATION_JSON);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.statusName").isString())
            .andExpect(jsonPath("$.data.purposeCodeName").isString());
    }

    @Test
    void getCarInfo_Success_WithETagModelChineseDescription() throws Exception {
        // 準備測試資料
        String plateNo = "RDV-6903";

        // 執行測試
        MockHttpServletRequestBuilder requestBuilder = get("/internal/subscribe/cars/{plateNo}", plateNo)
            .contentType(MediaType.APPLICATION_JSON);
            
        mockMvc.perform(requestBuilder)
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.changeLogs").isArray())
            .andExpect(jsonPath("$.data.changeLogs[*].changes[*].fieldChanges[?(@.fieldNameEn == 'etagModel')].oldValue",
                hasItem(either(nullValue()).or(anyOf(
                    is(ETagModelEnum.FrontGlass.getDescription()),
                    is(ETagModelEnum.Headlight.getDescription()),
                    is(ETagModelEnum.Plate.getDescription())
                )))))
            .andExpect(jsonPath("$.data.changeLogs[*].changes[*].fieldChanges[?(@.fieldNameEn == 'etagModel')].newValue",
                hasItem(either(nullValue()).or(anyOf(
                    is(ETagModelEnum.FrontGlass.getDescription()),
                    is(ETagModelEnum.Headlight.getDescription()),
                    is(ETagModelEnum.Plate.getDescription())
                )))));
    }

    @Test
    void updateCars_SetPrepWorkdaysToNull_Success() throws Exception {
        // 準備測試資料
        String plateNo = "RAG-2981";
        String updateRequest = "{\n" +
            "    \"carState\": \"OLD\",\n" +
            "    \"carModelCode\": \"S000S\",\n" +
            "    \"seat\": 5,\n" +
            "    \"energyType\": \"GASOLINE\",\n" +
            "    \"fuelType\": \"petrol95\",\n" +
            "    \"displacement\": 1798,\n" +
            "    \"currentMileage\": 59356,\n" +
            "    \"equipIds\": [],\n" +
            "    \"subscribeLevel\": 1,\n" +
            "    \"tagIds\": [],\n" +
            "    \"cnDesc\": null,\n" +
            "    \"type\": \"sedan\",\n" +
            "    \"gearType\": \"at\",\n" +
            "    \"mfgYear\": \"2019\",\n" +
            "    \"colorDesc\": \"銀\",\n" +
            "    \"etagModel\": null,\n" +
            "    \"etagNo\": null,\n" +
            "    \"vatNo\": null,\n" +
            "    \"prepWorkdays\": null,\n" +
            "    \"isSealandLaunched\": false\n" +
            "}";

        // 執行請求
        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/cars/{plateNo}", plateNo)
            .contentType(MediaType.APPLICATION_JSON)
            .content(updateRequest);

        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 驗證 prepWorkdays 已被設為 null
        Cars updatedCar = carsService.findByPlateNo(plateNo);
        assertNull(updatedCar.getPrepWorkdays());
    }

    @Test
    void addSingleCars_VirtualCar_RAA0099_Success() throws Exception {
        // 準備測試資料 - 使用虛擬車車牌號碼 RAA0099
        String plateNo = "RAA0099";

        String addSingleCarRequest = "{\n" +
            "    \"plateNo\": \"" + plateNo + "\",\n" +
            "    \"currentMileage\": 0,\n" +
            "    \"carModelCode\": \"S000P\",\n" +
            "    \"carState\": \"NEW\",\n" +
            "    \"subscribeLevel\": 29,\n" +
            "    \"launched\": \"open\",\n" +
            "    \"isSealandLaunched\": false,\n" +
            "    \"tagIds\": [3],\n" +
            "    \"equipIds\": [23, 22, 2, 21, 15, 16],\n" +
            "    \"gearType\": \"at\",\n" +
            "    \"colorDesc\": \"白色\",\n" +
            "    \"fuelType\": \"petrol95\",\n" +
            "    \"energyType\": \"GASOLINE\",\n" +
            "    \"vatNo\": \"12345678\",\n" +
            "    \"displacement\": 1997,\n" +
            "    \"seat\": 5,\n" +
            "    \"mfgYear\": 2025,\n" +
            "    \"prepWorkdays\": 7,\n" +
            "    \"carType\": \"sedan\",\n" +
            "    \"isVirtualCar\": true\n" +
            "}";

        // 執行新增請求
        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars/single")
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .contentType(MediaType.APPLICATION_JSON)
            .content(addSingleCarRequest);

        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 驗證車輛已成功新增
        requestBuilder = get("/internal/subscribe/cars/{plateNo}", plateNo)
            .contentType(MediaType.APPLICATION_JSON);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.plateNo").value(plateNo))
            .andExpect(jsonPath("$.data.carNo").isString())
            .andExpect(jsonPath("$.data.carModel.carModelCode").value("S000P"))
            .andExpect(jsonPath("$.data.colorDesc").value("白色"))
            .andExpect(jsonPath("$.data.prepWorkdays").value(7))
            .andExpect(jsonPath("$.data.currentMileage").value(0));

        // 進一步驗證虛擬車特性
        Cars addedCar = carsService.findByPlateNo(plateNo);
        assertNotNull(addedCar);
        assertTrue(addedCar.isVirtualCar());
        assertEquals(plateNo, addedCar.getPlateNo());
        assertEquals(0, addedCar.getCurrentMileage());
        assertEquals(7, addedCar.getPrepWorkdays());
    }
}