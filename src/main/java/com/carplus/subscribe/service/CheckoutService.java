package com.carplus.subscribe.service;

import carplus.common.enums.HeaderDefine;
import carplus.common.response.Result;
import carplus.common.response.exception.BadRequestException;
import carplus.common.utils.BeanUtils;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.dao.*;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.invoice.Invoices;
import com.carplus.subscribe.db.mysql.entity.payment.Account;
import com.carplus.subscribe.db.mysql.entity.payment.AccountDetail;
import com.carplus.subscribe.db.mysql.entity.payment.PaymentInfo;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.enums.finbus.InvoiceStatusEnum;
import com.carplus.subscribe.enums.finbus.PaymentMethodCodeEnum;
import com.carplus.subscribe.enums.finbus.TransactionItemCodeEnum;
import com.carplus.subscribe.enums.finbus.TypeEnum;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.feign.FinServiceBusClient;
import com.carplus.subscribe.model.OrderPriceInfoCriteria;
import com.carplus.subscribe.model.SecurityDepositInfo;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.finbus.*;
import com.carplus.subscribe.model.invoice.Invoice;
import com.carplus.subscribe.model.priceinfo.CalculateStage;
import com.carplus.subscribe.model.priceinfo.PriceInfoInterface;
import com.carplus.subscribe.model.queue.PaymentQueue;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.MattermostServer;
import com.carplus.subscribe.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.carplus.subscribe.enums.InvoiceDefine.InvType.Biz;
import static com.carplus.subscribe.enums.PayFor.SecurityDeposit;
import static com.carplus.subscribe.enums.PaymentCategory.PayAuth;
import static com.carplus.subscribe.enums.finbus.PaymentMethodCodeEnum.*;
import static com.carplus.subscribe.enums.finbus.TransactionItemCodeEnum.MONTHLY_CHARGE;
import static com.carplus.subscribe.enums.finbus.TypeEnum.CHARGE;
import static com.carplus.subscribe.enums.finbus.TypeEnum.REFUND;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;

@Service
@Slf4j
public class CheckoutService {


    @Autowired
    private OrderPriceInfoRepository orderPriceInfoRepository;
    @Autowired
    private AuthServer authServer;
    @Autowired
    private MattermostServer mattermostServer;
    @Autowired
    private PriceInfoService priceInfoService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private CarsService carsService;
    @Autowired
    private InvoiceServiceV2 invoiceService;
    @Autowired
    private PaymentInfoRepository paymentInfoRepository;
    @Autowired
    private AccountRepository accountRepository;
    @Autowired
    private AccountDetailRepository accountDetailRepository;
    @Autowired
    private EtagInfoRepository etagInfoRepository;
    @Autowired
    private FinServiceBusClient finServiceBusClient;

    public void advance(PaymentQueue queue) {
        Orders order = orderService.getOrder(queue.getOrderId());
        advance(queue, order);
    }


    @Transactional(transactionManager = "mysqlTransactionManager", propagation = Propagation.REQUIRES_NEW, noRollbackFor = {Exception.class, IOException.class})
    public void advance(PaymentQueue queue, Orders order) {
        BuildAccountReq buildAccountReq = null;
        try {
            buildAccountReq = generateBuildAccountReq(order, new Date());
            TransactionItemReq transactionItemReq = createTransactionItem(CHARGE, TransactionItemCodeEnum.TAPPAY_ADVANCE, queue.getAmount());
            List<TransactionItemReq> transactionItemReqList = Collections.singletonList(transactionItemReq);
            buildAccountReq.setTransactionItems(transactionItemReqList);
            PaymentMethodReq paymentMethodReq = createPaymentMethod(CHARGE, PaymentMethodCodeEnum.getPaymentMethodCodeEnum(queue.getCardNumber()), queue.getAmount(), queue.getTransactionNumber(), transactionItemReqList);
            paymentMethodReq.setTransactionItems(transactionItemReqList);
            buildAccountReq.setPaymentMethods(Collections.singletonList(paymentMethodReq));
            Result<?> result = finServiceBusClient.checkout(HeaderDefine.Platform.SERVER, HeaderDefine.SystemKind.SUB, buildAccountReq);
            if (result.getStatusCode() != 0) {
                log.error("應收帳款立帳失敗，{}", result.getMessage());
                Map<String, Object> alert = new LinkedHashMap<>();
                alert.put("request", buildAccountReq);
                alert.put("response", result);
                mattermostServer.notify(String.format("%s,OrderNo:%s", ADVANCE_CHECK_OUT_FAIL.getMsg(), order.getOrderNo()), alert, null);
            }
        } catch (Exception e) {
            Map<String, Object> alert = new LinkedHashMap<>();
            alert.put("Queue", queue);
            if (buildAccountReq != null) {
                alert.put("BuildAccountReq", buildAccountReq);
            }
            mattermostServer.notify(String.format("%s,OrderNo:%s", ADVANCE_CHECK_OUT_FAIL.getMsg(), order.getOrderNo()), alert, e);
        }

    }

    public void modifyMonthlyItem(Orders order) {
        List<OrderPriceInfo> orderPriceInfoList =
            orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder().orderNo(Collections.singletonList(order.getOrderNo())).category(Collections.singletonList(PriceInfoDefinition.PriceInfoCategory.MonthlyFee)).build());
        modifyMonthlyItem(order, orderPriceInfoList);
    }

    public void modifyMonthlyItem(Orders order, List<OrderPriceInfo> orderPriceInfoList) {
        if ((order.getStatus() == OrderStatus.DEPART.getStatus() && order.getStartDate() != null && !DateUtils.isSameDay(Date.from(order.getStartDate()), Date.from(order.getExpectStartDate())))
            || order.getStatus() == OrderStatus.BOOKING.getStatus()) {
            orderPriceInfoList = orderPriceInfoList.stream().filter(opi -> opi.getCategory() == PriceInfoDefinition.PriceInfoCategory.MonthlyFee && opi.isPaid()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orderPriceInfoList)) {
                Map<Integer, CalculateStage> calculateStageMap = DateUtil.calculateStageAndDate(order).stream().collect(Collectors.toMap(CalculateStage::getStage, Function.identity()));
                ModifyMonthlyItemReq modifyMonthlyItemReq = new ModifyMonthlyItemReq(order.getOrderNo());
                modifyMonthlyItemReq.setPeriods(new ArrayList<>());
                modifyMonthlyItemReq.setOrderNumber(order.getOrderNo());
                for (OrderPriceInfo orderPriceInfo : orderPriceInfoList) {
                    CalculateStage stage = calculateStageMap.get(orderPriceInfo.getStage());
                    if (stage == null) {
                        continue;
                    }
                    modifyMonthlyItemReq.getPeriods().add(new ModifyMonthlyItemReq.Period(stage.getStartDate(), stage.getEndDate(), orderPriceInfo.getActualPrice()));
                }
                modifyMonthlyItemReq.setTransactionTime(new Date());
                finServiceBusClient.modifyMonthlyItem(HeaderDefine.Platform.SERVER, HeaderDefine.SystemKind.SUB, modifyMonthlyItemReq);
            }
        }
    }


    @Transactional(transactionManager = "mysqlTransactionManager", propagation = Propagation.REQUIRES_NEW)
    public void checkOutWithNewTransaction(Orders order) {
        checkOut(order, new Date(Instant.now().minus(2, ChronoUnit.HOURS).toEpochMilli()));
    }

    /**
     * 立帳
     */
//    @Async
    @Transactional(transactionManager = "mysqlTransactionManager", propagation = Propagation.REQUIRES_NEW)
    public void checkOut(String orderNo) {
        Orders orders = orderService.getOrder(orderNo);
        checkOut(orders);
    }


    @Transactional(transactionManager = "mysqlTransactionManager", propagation = Propagation.REQUIRES_NEW)
    public void checkOut(Orders order) {
        checkOut(order, null);
    }

    /**
     * 立帳
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void checkOut(Orders order, Date transactionDate) {

        String orderNo = order.getOrderNo();

        // 取得帳目資訊
        List<Account> accounts = accountRepository.getAccountsByOrderNo(orderNo);

        // 取得發票資訊
        List<Invoices> invoicesList = invoiceService.getInvoice(orderNo).stream()
            .filter(inv -> !Objects.equals(inv.getIsCheckout(), InvoiceDefine.InvCheckoutStatus.NONE_CHECKOUT.getStatus()))
            .collect(Collectors.toList());

        // 取得 ETag 資訊
        List<ETagInfo> etagInfoList = etagInfoRepository.getETagInfosByOrderNo(orderNo).stream()
            .filter(etag -> !etag.isUploaded() && etag.getPaidETagAmt() != null && etag.getPaidETagAmt() > 0 && etag.getOrderPriceInfoId() != null)
            .collect(Collectors.toList());

        BuildAccountReq buildAccountReq = generateBuildAccountReq(order, transactionDate, accounts, invoicesList, etagInfoList);
        // 如果沒有變更，則不進行立帳
        if (isCheckoutNotNeeded(buildAccountReq)) {
            log.info("No changes, checkout not needed");
            return;
        }

        List<Long> accountIds = accounts.stream().map(Account::getId).collect(Collectors.toList());
        doCheckout(order, buildAccountReq, accountIds, invoicesList, etagInfoList);
        invoiceService.cleanInvoiceCache(orderNo);
    }

    private List<PaymentMethodReq> getPaymentMethodReqs(Orders order,
                                                        Map<Long, List<AccountDetail>> accountDetailMap,
                                                        Map<Long, Account> accountMap,
                                                        AtomicInteger paymentAmt,
                                                        Map<Integer, OrderPriceInfo> orderPriceInfoMap) {
        Map<Long, Integer> accountDetailSumAmt = new HashMap<>();
        accountDetailMap.forEach((accountId, accountDetails) -> {
            int sum = accountDetails.stream().mapToInt(AccountDetail::getAmount).sum();
            accountDetailSumAmt.put(accountId, sum);
        });
        return accountDetailSumAmt.entrySet()
            .stream()
            .filter(entry -> !Objects.equals(entry.getValue(), 0))
            .peek(entry -> paymentAmt.addAndGet(entry.getValue()))
            .map(entry -> createPaymentMethodReq(order, entry, accountDetailMap.getOrDefault(entry.getKey(), new ArrayList<>()), accountMap, orderPriceInfoMap))
            .collect(Collectors.toList());
    }

    private PaymentMethodReq createPaymentMethodReq(Orders order,
                                                    Map.Entry<Long, Integer> entry,
                                                    List<AccountDetail> details,
                                                    Map<Long, Account> accountMap,
                                                    Map<Integer, OrderPriceInfo> orderPriceInfoMap) {
        Integer unCheckoutAmount = entry.getValue();
        int absUnCheckoutAmount = Math.abs(unCheckoutAmount);
        TypeEnum paymentMethodReqType = unCheckoutAmount > 0 ? CHARGE : TypeEnum.REFUND;

        Account acc = accountMap.get(entry.getKey());
        PaymentMethodCodeEnum paymentMethodCodeEnum = StringUtils.isNotBlank(acc.getTradeId())
            ? (paymentMethodReqType == CHARGE ? TAPPAY_ADVANCE : PaymentMethodCodeEnum.getPaymentMethodCodeEnum(acc.getCardNumber()))
            : PaymentMethodCodeEnum.REMIT;
        String referenceKey = paymentMethodCodeEnum == REMIT ? String.valueOf(acc.getRemitNo()) : acc.getTransactionNumber();
        List<TransactionItemReq> transactionItems = createTransactionItems(details, orderPriceInfoMap, order);
        return createPaymentMethod(paymentMethodReqType, paymentMethodCodeEnum, absUnCheckoutAmount, referenceKey, transactionItems);
    }

    private TransactionItemReq createTransactionItem(TypeEnum type, TransactionItemCodeEnum code, Integer amount) {
        return createTransactionItem(type, code, amount, null, null);
    }

    private TransactionItemReq createTransactionItem(TypeEnum type, TransactionItemCodeEnum code, Integer amount, Instant apportionStartDate, Instant apportionEndDate) {
        return TransactionItemReq.builder()
            .type(type.name())
            .code(code.name())
            .amount(amount)
            .apportionEndDate(apportionEndDate)
            .apportionStartDate(apportionStartDate)
            .build();
    }


    private List<TransactionItemReq> createTransactionItems(Orders order, Map<Integer, OrderPriceInfo> orderPriceInfoMap) {
        List<TransactionItemReq> transactionItems = new ArrayList<>();
        TransactionItemCodeEnum transactionItem = priceInfoService.areAllPriceInfosMerchandiseRelated(new ArrayList<>(orderPriceInfoMap.values()), null)
            ? TransactionItemCodeEnum.MERCHANDISE : TransactionItemCodeEnum.RENT;
        if (transactionItem == TransactionItemCodeEnum.MERCHANDISE) {
            int amount = orderPriceInfoMap.values().stream().mapToInt(PriceInfoInterface::getActualReceivePrice).sum();
            if (amount != 0) {
                transactionItems.add(createTransactionItem(amount > 0 ? CHARGE : REFUND, transactionItem, amount));
            }
        } else {
            Map<Integer, CalculateStage> calculateStageMap = DateUtil.calculateStageAndDate(order).stream().collect(Collectors.toMap(CalculateStage::getStage, Function.identity()));
            Map<Integer, Integer> orderPriceInfoGroup = new HashMap<>();
            orderPriceInfoMap.forEach((id, opi) -> {
                if (opi.getActualPrice() == 0) {
                    return;
                }
                Integer key = opi.getRefPriceInfoNo() == null ? opi.getId() : opi.getRefPriceInfoNo();
                orderPriceInfoGroup.putIfAbsent(key, 0);
                orderPriceInfoGroup.put(key, orderPriceInfoGroup.get(key) + opi.getActualPrice());
            });
            orderPriceInfoGroup.forEach((id, amt) -> {
                OrderPriceInfo orderPriceInfo = orderPriceInfoMap.get(id);
                if (orderPriceInfo == null) {
                    log.error("立帳查不到對應款項資訊, orderNo:{}, id:{}", order.getOrderNo(), id);
                }
                if (amt == 0) {
                    return;
                }
                TypeEnum type = amt > 0 ? CHARGE : REFUND;
                // 月費需要給攤提時間與不同交易項目
                boolean isMonthlyFee = orderPriceInfo.getCategory() == PriceInfoDefinition.PriceInfoCategory.MonthlyFee;
                isMonthlyFee = isMonthlyFee || Optional.ofNullable(orderPriceInfo.getRefPriceInfoNo()).map(orderPriceInfoMap::get).map(opi2 -> opi2.getCategory().equals(PriceInfoDefinition.PriceInfoCategory.MonthlyFee)).orElse(false);
                if (isMonthlyFee) {
                    CalculateStage stage = calculateStageMap.get(orderPriceInfo.getStage());
                    transactionItems.add(
                        createTransactionItem(type, MONTHLY_CHARGE, amt, compareDate(stage.getStartDate(), order.getEndDate()), compareDate(stage.getEndDate(), order.getEndDate())));
                } else {
                    transactionItems.add(createTransactionItem(type, transactionItem, amt));
                }
            });
        }
        return transactionItems;
    }


    private List<TransactionItemReq> createTransactionItems(List<AccountDetail> details, Map<Integer, OrderPriceInfo> orderPriceInfoMap, Orders order) {
        List<TransactionItemReq> reqList = new ArrayList<>();
        for (AccountDetail accountDetail : details) {
            if (MapUtils.isEmpty(accountDetail.getOrderPriceAmounts())) {
                TypeEnum type = accountDetail.getAmount() > 0 ? CHARGE : REFUND;
                reqList.add(createTransactionItem(type, TransactionItemCodeEnum.RENT, Math.abs(accountDetail.getAmount())));
            } else {
                Map<Integer, CalculateStage> calculateStageMap = DateUtil.calculateStageAndDate(order).stream().collect(Collectors.toMap(CalculateStage::getStage, Function.identity()));

                for (Map.Entry<Integer, Integer> entry : accountDetail.getOrderPriceAmounts().entrySet()) {
                    OrderPriceInfo opi = orderPriceInfoMap.get(entry.getKey());
                    TypeEnum type = opi.getActualPrice() > 0 ? CHARGE : REFUND;
                    TransactionItemCodeEnum transactionItemCodeEnum = TransactionItemCodeEnum.getTransactionItemCodeEnum(opi.getCategory());
                    if (opi.getActualPrice() == 0 || opi.getCategory() == PriceInfoDefinition.PriceInfoCategory.ETag) {
                        continue;
                    }
                    int amount = Math.abs(entry.getValue());
                    if (CollectionUtils.isNotEmpty(opi.getRemitAccountIds()) && opi.getRemitAccountIds().size() > 1) {
                        amount = Math.min(Math.abs(entry.getValue()), Math.abs(accountDetail.getAmount()));
                    }
                    if (transactionItemCodeEnum == MONTHLY_CHARGE) {
                        CalculateStage stage = calculateStageMap.get(opi.getStage());
                        reqList.add(createTransactionItem(type, TransactionItemCodeEnum.RENT, amount, compareDate(stage.getStartDate(), order.getEndDate()),
                            compareDate(stage.getEndDate(), order.getEndDate())));

                    } else {
                        reqList.add(createTransactionItem(type, TransactionItemCodeEnum.RENT, amount));
                    }

                }
            }

        }
        return reqList;
    }

    /**
     * 比較日期，若 actualDate 為 null 則回傳 expectDate
     */
    private Instant compareDate(Instant expectDate, Instant actualDate) {
        if (actualDate == null) {
            return expectDate;
        }
        return expectDate.isBefore(actualDate) ? expectDate : actualDate;
    }

    private List<TransactionItemReq> buildTransactionItems(List<TransactionItemReq> transactionItems, List<ETagInfo> etagInfoList) {
        return Stream.concat(
            transactionItems.stream(),
            etagInfoList.stream().map(etagInfo -> createTransactionItem(
                CHARGE,
                TransactionItemCodeEnum.ETAG,
                Math.abs(etagInfo.getPaidETagAmt())
            ))
        ).collect(Collectors.toList());
    }

    private PaymentMethodReq createPaymentMethod(TypeEnum type, PaymentMethodCodeEnum code, Integer amount, String referenceKey, List<TransactionItemReq> transactionItems) {
        return PaymentMethodReq.builder()
            .type(type.name())
            .code(code.name())
            .amount(amount)
            .referenceKey(referenceKey)
            .transactionItems(transactionItems)
            .build();
    }


    private void validateAmounts(String orderNo, AtomicInteger paymentAmt, AtomicInteger invoiceAmt) {
        if (paymentAmt.get() != invoiceAmt.get()) {
            Map<String, Object> alert = new LinkedHashMap<>();
            alert.put("發票金額", invoiceAmt.get());
            alert.put("帳務金額", paymentAmt.get());
            mattermostServer.notify(String.format("%s,OrderNo:%s", CHECK_OUT_FAIL.getMsg(), orderNo), alert, null);
            throw new SubscribeException(PRICE_AND_INVOICE_AMOUNT_NOT_EQUAL);
        }
    }

    private void processEtagPayments(List<ETagInfo> etagInfoList, Map<Long, Account> accountMap, Map<Integer, PaymentInfo> paymentInfoIdMap, List<PaymentMethodReq> paymentMethods) {
        for (ETagInfo eTagInfo : etagInfoList) {
            OrderPriceInfo orderPriceInfo = orderPriceInfoRepository.findById(eTagInfo.getOrderPriceInfoId())
                .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));

            TypeEnum type = CHARGE;
            Integer amount = orderPriceInfo.getReceivedAmount();
            TransactionItemReq transactionItem = createTransactionItem(type, TransactionItemCodeEnum.ETAG, amount);

            String referenceKey;

            if (orderPriceInfo.getPaymentId() != null) {
                PaymentInfo paymentInfo = paymentInfoIdMap.get(orderPriceInfo.getPaymentId());
                referenceKey = paymentInfo.getTransactionNumber();

                if (amount.equals(paymentInfo.getAmount())) {
                    paymentMethods.add(createPaymentMethod(type, TAPPAY_ADVANCE, amount, referenceKey, Collections.singletonList(transactionItem)));
                } else {
                    boolean isExist = updateExistingPaymentMethod(paymentMethods, referenceKey, type, amount, transactionItem);
                    if (!isExist) {
                        paymentMethods.add(createPaymentMethod(type, TAPPAY_ADVANCE, amount, referenceKey, Collections.singletonList(transactionItem)));
                    }
                }
            } else {
                List<Account> accounts = orderPriceInfo.getRemitAccountIds().stream().map(accountMap::get).collect(Collectors.toList());

                if (accounts.size() > 1) {
                    throw new SubscribeException(CHECK_OUT_ETAG_MULTIPLE_REMIT_FAIL);
                }

                Account account = accounts.get(0);
                referenceKey = String.valueOf(account.getRemitNo());

                if (account.getAmount() == 0) {
                    paymentMethods.add(createPaymentMethod(type, PaymentMethodCodeEnum.REMIT, amount, referenceKey, Collections.singletonList(transactionItem)));
                } else {
                    boolean isExist = updateExistingPaymentMethod(paymentMethods, referenceKey, type, amount, transactionItem);
                    if (!isExist) {
                        paymentMethods.add(createPaymentMethod(type, PaymentMethodCodeEnum.REMIT, amount, referenceKey, Collections.singletonList(transactionItem)));
                    }
                }
            }
        }
    }

    private boolean updateExistingPaymentMethod(List<PaymentMethodReq> paymentMethods, String referenceKey, TypeEnum type, Integer amount, TransactionItemReq transactionItem) {
        AtomicBoolean isUpdated = new AtomicBoolean(false);
        paymentMethods.stream()
            .filter(p -> p.getReferenceKey().equals(referenceKey) && p.getType().equals(type.name()))
            .findAny()
            .ifPresent(paymentMethodReq -> {
                paymentMethodReq.setAmount(paymentMethodReq.getAmount() + amount);
                List<TransactionItemReq> transactionItems = new ArrayList<>(paymentMethodReq.getTransactionItems());
                transactionItems.add(transactionItem);
                paymentMethodReq.setTransactionItems(transactionItems);
                isUpdated.set(true);
            });
        return isUpdated.get();
    }

    private boolean isCheckoutNotNeeded(BuildAccountReq buildAccountReq) {
        return buildAccountReq.getPaymentMethods().isEmpty()
            && buildAccountReq.getTransactionItems().isEmpty()
            && buildAccountReq.getInvoices().isEmpty();
    }

    public void doCheckout(Orders order, BuildAccountReq buildAccountReq, List<Long> accountIds,
                           List<Invoices> invoicesList, List<ETagInfo> etagInfoList) {

        Result<?> result = finServiceBusClient.checkout(HeaderDefine.Platform.SERVER, HeaderDefine.SystemKind.SUB, buildAccountReq);
        if (result.getStatusCode() != 0) {
            log.error("立帳失敗，{}", result.getMessage());
            Map<String, Object> alert = new LinkedHashMap<>();
            alert.put("request", buildAccountReq);
            alert.put("response", result);
            mattermostServer.notify(String.format("%s,OrderNo:%s", CHECK_OUT_FAIL.getMsg(), order.getOrderNo()), alert, null);
            throw new SubscribeException(CHECK_OUT_FAIL);
        }

        try {
            updateCheckoutStatuses(accountIds, invoicesList, etagInfoList);
        } catch (Exception e) {
            Map<String, Object> alert = new LinkedHashMap<>();
            alert.put("request", buildAccountReq);
            alert.put("exception", e.getMessage());
            mattermostServer.notify(String.format("%s,OrderNo:%s", "登打狀態異動失敗", order.getOrderNo()), alert, e);
        }

        log.info("立帳回傳資訊:{}", result);
    }

    // 要執行Redis AOP要宣告為Public
    public void updateCheckoutStatuses(List<Long> accountIds, List<Invoices> invoicesList, List<ETagInfo> etagInfoList) {
        accountDetailRepository.updateCheckout(accountIds);
        invoiceService.setCheckout(invoicesList);
        etagInfoRepository.updateCheckout(etagInfoList.stream().map(ETagInfo::getId).collect(Collectors.toList()));
    }


    private BuildAccountReq generateBuildAccountReq(Orders order) {
        return generateBuildAccountReq(order, null);
    }

    private BuildAccountReq generateBuildAccountReq(Orders order, Date transactionDate) {

        MainContract mainContract = order.getContract().getMainContract();
        AuthUser authUser = authServer.getUserWithRetry(mainContract.getAcctId());

        return BuildAccountReq.builder()
            .departmentCode(CarPlusConstant.SUBSCRIBE_MANAGEMENT_DEPT_CODE)
            .orderNumber(order.getOrderNo())
            .contractNumber(mainContract.getMainContractNo())
            .carNumber(mainContract.getPlateNo())
            .customerName(authUser.getAcctName())
            .customerIdNumber(authUser.getLoginId())
            .transactionTime(transactionDate == null ? new Date() : transactionDate)
            .startDate(new Date(Optional.ofNullable(order.getStartDate()).orElse(order.getExpectStartDate()).toEpochMilli()))
            .endDate(new Date(Optional.ofNullable(order.getEndDate()).orElse(order.getExpectEndDate()).toEpochMilli()))
            .customerType("NATURAL")
            .transactionItems(new ArrayList<>())
            .invoices(new ArrayList<>())
            .paymentMethods(new ArrayList<>())
            // 訂單是否期滿結束(false表示提前解約)
            // true: 實際結束日期 >= 預期結束日期 (期滿)
            // false: 1. 尚未結束(結束日期為空) 2. 實際結束日期 < 預期結束日期 (提前解約)
            .isFinish(Optional.ofNullable(order.getEndDate())
                .map(DateUtil::convertToStartOfInstant)
                .map(actualEnd -> !actualEnd.isBefore(DateUtil.convertToStartOfInstant(order.getExpectEndDate())))
                .orElse(false))
            .build();
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public BuildAccountReq generateBuildAccountReq(Orders order, Date transactionDate, List<Account> accounts, List<Invoices> invoicesList, List<ETagInfo> etagInfoList) {

        BuildAccountReq buildAccountReq = generateBuildAccountReq(order, transactionDate);

        // 帳目資訊
        Map<Long, Account> accountMap = accounts.stream()
            .collect(Collectors.toMap(Account::getId, Function.identity()));
        List<Long> accountIds = accounts.stream()
            .map(Account::getId)
            .collect(Collectors.toList());
        // 取得未結帳金額
        Map<Long, List<AccountDetail>> accountDetailMap = accountDetailRepository.findUncheckOutAmountByAccountIds(accountIds);

        // 付款資訊
        List<PaymentInfo> paymentInfoList = getPaymentsByOrder(order.getOrderNo()).stream()
            .filter(p -> PayAuth == p.getPaymentCategory())
            .collect(Collectors.toList());

        Map<Integer, PaymentInfo> paymentInfoIdMap = paymentInfoList.stream()
            .collect(Collectors.toMap(PaymentInfo::getPaymentId, Function.identity()));

        Map<Integer, OrderPriceInfo> orderPriceInfoMap = orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder().orderNo(Collections.singletonList(order.getOrderNo())).build()).stream()
            .collect(Collectors.toMap(OrderPriceInfo::getId, Function.identity()));


        AtomicInteger invoiceAmt = new AtomicInteger();
        AtomicInteger paymentAmt = new AtomicInteger();

        // 計算發票金額
        invoicesList.forEach(i -> {
            if (InvoiceDefine.InvStatus.CREATE.name().equals(i.getStatus())) {
                invoiceAmt.addAndGet(i.getAmount());
            } else if (!i.isMultipleCheckout()) {
                invoiceAmt.addAndGet(-i.getAmount());
            }
        });

        // 設置發票請求
        List<InvoiceReq> invoiceReqList = generateCheckoutInvoiceReq(order, invoicesList, orderPriceInfoMap);
        buildAccountReq.setInvoices(invoiceReqList);

        // 從發票請求提取交易項目
        List<TransactionItemReq> transactionItems = invoiceReqList.stream().map(InvoiceReq::getTransactionItems).flatMap(Collection::stream).collect(Collectors.toList());

        // 設置付款方式
        List<PaymentMethodReq> paymentMethods = getPaymentMethodReqs(order, accountDetailMap, accountMap, paymentAmt, orderPriceInfoMap);
        buildAccountReq.setPaymentMethods(paymentMethods);

        // 驗證金額
        validateAmounts(order.getOrderNo(), paymentAmt, invoiceAmt);

        // 設置交易項目
        buildAccountReq.setTransactionItems(buildTransactionItems(transactionItems, etagInfoList));

        // 處理 ETag 付款
        processEtagPayments(etagInfoList, accountMap, paymentInfoIdMap, paymentMethods);

        return buildAccountReq;
    }

    private List<InvoiceReq> generateCheckoutInvoiceReq(Orders order, List<Invoices> invoicesList, Map<Integer, OrderPriceInfo> orderPriceInfoMap) {
        Map<Boolean, List<Invoices>> partitionedInvoices = invoicesList.stream()
            .collect(Collectors.partitioningBy(Invoices::isMultipleCheckout));

        List<InvoiceReq> invoiceReqList = partitionedInvoices.get(false).stream()
            .map(invoice -> {
                InvoiceStatusEnum type = Optional.ofNullable(InvoiceStatusEnum.of(InvoiceDefine.InvStatus.valueOf(invoice.getStatus())))
                    .orElseThrow(() -> new IllegalStateException("無效的發票狀態: " + invoice.getStatus()));
                TypeEnum transactionType = type == InvoiceStatusEnum.NEW ? CHARGE : TypeEnum.REFUND;
                List<TransactionItemReq> transactionItems = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(invoice.getRefPriceInfoIds())) {
                    transactionItems.addAll(createTransactionItems(order, getInvoiceOrderPriceInfos(invoice.getRefPriceInfoIds(), orderPriceInfoMap)));
                } else {
                    transactionItems.add(createTransactionItem(transactionType, TransactionItemCodeEnum.RENT, invoice.getAmount()));
                }
                if (transactionType == REFUND) {
                    upSideDownTransactionType(transactionItems);
                }
                return createInvoiceReq(type, invoice.getInvNo(), invoice.getAmount(), getBusinessIdNumber(invoice), transactionItems);
            })
            .collect(Collectors.toList());

        // 處理發票開立後又作廢的情況，補充一筆開立的資訊
        invoiceReqList.addAll(
            partitionedInvoices.get(true).stream()
                .map(invoice -> {
                    List<TransactionItemReq> transactionItems = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(invoice.getRefPriceInfoIds())) {
                        transactionItems.addAll(createTransactionItems(order, getInvoiceOrderPriceInfos(invoice.getRefPriceInfoIds(), orderPriceInfoMap)));
                    } else {
                        transactionItems.add(createTransactionItem(CHARGE, TransactionItemCodeEnum.RENT, invoice.getAmount()));
                    }
                    return createInvoiceReq(InvoiceStatusEnum.NEW, invoice.getInvNo(), invoice.getAmount(), getBusinessIdNumber(invoice), transactionItems);
                })
                .collect(Collectors.toList())
        );
        // 作一筆反向發票沖銷帳
        invoiceReqList.addAll(
            partitionedInvoices.get(true).stream()
                .map(invoice -> {
                    List<TransactionItemReq> transactionItems = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(invoice.getRefPriceInfoIds())) {
                        transactionItems = createTransactionItems(order, getInvoiceOrderPriceInfos(invoice.getRefPriceInfoIds(), orderPriceInfoMap));
                        upSideDownTransactionType(transactionItems);
                    } else {
                        transactionItems.add(createTransactionItem(REFUND, TransactionItemCodeEnum.RENT, invoice.getAmount()));
                    }
                    return createInvoiceReq(InvoiceStatusEnum.VOID, invoice.getInvNo(), invoice.getAmount(), getBusinessIdNumber(invoice), transactionItems);
                })
                .collect(Collectors.toList())
        );

        return invoiceReqList;
    }

    private static void upSideDownTransactionType(List<TransactionItemReq> transactionItems) {
        transactionItems.forEach(transactionItemReq -> {
            if (transactionItemReq.getType().equals(CHARGE.name())) {
                transactionItemReq.setType(REFUND.name());
            } else if (transactionItemReq.getType().equalsIgnoreCase(REFUND.name())) {
                transactionItemReq.setType(CHARGE.name());
            }
        });
    }


    private Map<Integer, OrderPriceInfo> getInvoiceOrderPriceInfos(List<Integer> ids, Map<Integer, OrderPriceInfo> orderPriceInfoMap) {
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyMap();

        }
        Map<Integer, OrderPriceInfo> result = ids.stream()
            .map(orderPriceInfoMap::get)
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(OrderPriceInfo::getId, Function.identity()));
        if (result.size() != ids.size()) {
            throw new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND);
        }
        return result;
    }

    private InvoiceReq createInvoiceReq(InvoiceStatusEnum type, String number, Integer amount, String businessIdNumber, List<TransactionItemReq> transactionItems) {
        return InvoiceReq.builder()
            .type(type.name())
            .number(number)
            .amount(amount)
            .businessIdNumber(businessIdNumber)
            .transactionItems(transactionItems)
            .build();
    }

    private String getBusinessIdNumber(Invoices invoice) {
        return Optional.ofNullable(invoice.getInvoice())
            .filter(inv -> Objects.equals(inv.getType(), Biz.getType()))
            .map(Invoice::getId)
            .orElse(null);
    }

    /**
     * 拿取訂單所有付款資訊
     */
    public List<PaymentInfo> getPaymentsByOrder(String orderNo) {
        return paymentInfoRepository.getPaymentInfosByOrderNo(orderNo);
    }


    @Transactional(transactionManager = "mysqlTransactionManager")
    public void securityDepositCheckOut(Orders order, PaymentQueue queue, boolean isManualRefund) {
        MainContract mainContract = order.getContract().getMainContract();

        if (mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit() == 0) {
            return;
        }

        Cars cars = carsService.findByPlateNo(mainContract.getPlateNo());

        BuildAccountReq buildAccountReq = generateBuildAccountReq(order);
        updateCarNumber(cars, buildAccountReq);
        updateTransactionTime(isManualRefund, queue, mainContract, buildAccountReq);

        buildAccountReq.setInvoices(new ArrayList<>());

        TypeEnum type = PaymentCategory.Refund.equals(queue.getPaymentCategory()) ? TypeEnum.REFUND : CHARGE;
        List<TransactionItemReq> transactionItems = Collections.singletonList(createTransactionItem(type, TransactionItemCodeEnum.MARGIN, queue.getAmount()));
        buildAccountReq.setTransactionItems(transactionItems);

        PaymentInfo securityDeposit = getSecurityDepositPayment(order, type);
        PaymentMethodCodeEnum code = isAECard(queue.getCardNumber()) || (securityDeposit != null && isAECard(securityDeposit.getCardNumber())) ? TAPPAY_NCC : TAPPAY_TSIB;
        String referenceKey = securityDeposit == null ? queue.getTransactionNumber() : securityDeposit.getTransactionNumber();
        PaymentMethodReq paymentMethod = createPaymentMethod(type, code, queue.getAmount(), referenceKey, transactionItems);
        buildAccountReq.setPaymentMethods(Collections.singletonList(paymentMethod));

        doSecurityDepositCheckout(order, buildAccountReq);
    }

// Helper methods extracted for readability and reuse

    private void updateCarNumber(Cars cars, BuildAccountReq buildAccountReq) {
        if (cars.isVirtualCar()) {
            buildAccountReq.setCarNumber(null);
        }
    }

    private void updateTransactionTime(boolean isManualRefund, PaymentQueue queue, MainContract mainContract, BuildAccountReq buildAccountReq) {
        if (isManualRefund && PaymentCategory.Refund.equals(queue.getPaymentCategory())) {
            SecurityDepositInfo securityDepositInfo = mainContract.getOriginalPriceInfo().getSecurityDepositInfo();
            buildAccountReq.setTransactionTime(Optional.ofNullable(securityDepositInfo.getManualRefundUpdateDate())
                .orElseThrow(() -> new SubscribeException(MANUAL_REFUND_SECURITY_DEPOSIT_TIME_NOT_FUND)));
        }
    }

    private PaymentInfo getSecurityDepositPayment(Orders order, TypeEnum type) {
        if (type == TypeEnum.REFUND) {
            return getPaymentsByOrder(order.getOrderNo()).stream()
                .filter(p -> PayAuth.equals(p.getPaymentCategory())
                    && p.getPayFor().equals(PayFor.SecurityDeposit)
                    && (p.getStatus() == OrderPaymentStatus.AUTHORIZED.getCode()
                    || p.getStatus() == OrderPaymentStatus.BANK_CAPTURE.getCode()))
                .findAny()
                .orElseThrow(() -> new BadRequestException("保證金退款時，找不到保證金付款資料"));
        }
        return null;
    }

    private void doSecurityDepositCheckout(Orders order, BuildAccountReq buildAccountReq) {
        Result<?> result = null;
        try {
            result = finServiceBusClient.checkout(HeaderDefine.Platform.SERVER, HeaderDefine.SystemKind.SUB, buildAccountReq);
            if (result.getStatusCode() != 0) {
                throw new BadRequestException(result.getMessage());
            }
            log.info("立帳回傳資訊:{}", result);
        } catch (Exception e) {
            log.error("{}，{}", CHECK_OUT_SECURITY_DEPOSIT_FAIL.getMsg(), Optional.ofNullable(result).map(Result::getMessage).orElse(e.getMessage()));
            Map<String, Object> alert = new LinkedHashMap<>();
            alert.put("request", buildAccountReq);
            alert.put("response", result);
            mattermostServer.notify(String.format("%s,OrderNo:%s", CHECK_OUT_SECURITY_DEPOSIT_FAIL.getMsg(), order.getOrderNo()), alert, null);
        }
    }


    /**
     * 人工退款立帳
     */
    public void manualRefundSecurityDepositCheckOut(String orderNo) {
        Orders orders = orderService.getOrder(orderNo);
        manualRefundSecurityDepositCheckOut(orders);
    }

    public void manualRefundSecurityDepositCheckOut(Orders orders) {
        PaymentInfo paymentInfo = paymentInfoRepository.getExpiredPaymentInfosByOrderNo(orders.getOrderNo()).stream().filter(p -> p.getStatus() == OrderPaymentStatus.EXPIRED_REFUND.getCode()).findAny()
            .orElseThrow(() -> new SubscribeException(MANUAL_REFUND_SECURITY_DEPOSIT_NOT_FUND));
        PaymentQueue paymentQueue = BeanUtils.copyProperties(paymentInfo, new PaymentQueue());
        paymentQueue.setPaymentId(paymentQueue.getPaymentId());
        paymentQueue.setPaymentCategory(paymentQueue.getPaymentCategory());
        paymentQueue.setAmount(paymentInfo.getAmount());
        paymentQueue.setTransactionNumber(paymentInfo.getTransactionNumber());
        paymentQueue.setTradeId(paymentInfo.getTradeId());
        paymentQueue.setRefundId(paymentInfo.getRefundId());
        securityDepositCheckOut(orders, paymentQueue, true);
    }

    /**
     * 取消訂單立帳
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void orderRefundCheckOut(Orders order, OrderPriceInfo refundOrderPriceInfo) {
        orderRefundCheckOut(order, refundOrderPriceInfo, false);
    }

    /**
     * 取消訂單立帳
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void orderRefundCheckOut(Orders order, OrderPriceInfo refundOrderPriceInfo, boolean isLegal) {
        invoiceService.cleanInvoiceCache(order.getOrderNo());
        /*
         * 交易方式：RENT_SUBSCRIBE收入付款6000,  MARGIN退款4000
         * 付款方式：MARGIN收入付款6000,TAPPAY_TSIB退款4000
         */
        MainContract mainContract = order.getContract().getMainContract();
        Cars cars = carsService.findByPlateNo(mainContract.getPlateNo());
        BuildAccountReq buildAccountReq = generateBuildAccountReq(order);
        updateCarNumber(cars, buildAccountReq);

        List<OrderPriceInfo> orderPriceInfoList = orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder().orderNo(Collections.singletonList(order.getOrderNo())).build());
        Map<Integer, OrderPriceInfo> orderPriceInfoMap = orderPriceInfoList.stream()
            .collect(Collectors.toMap(OrderPriceInfo::getId, Function.identity()));

        if (refundOrderPriceInfo != null) {
            orderPriceInfoMap.put(refundOrderPriceInfo.getId(), refundOrderPriceInfo);
        }

        PaymentInfo securityDepositPayment = getPaymentsByOrder(order.getOrderNo()).stream()
            .filter(p -> SecurityDeposit == p.getPayFor()).findAny().orElse(null); // null 代表 order 為續約單
        if (securityDepositPayment == null) {
            if (order.getIsNewOrder() && mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getPaidSecurityDeposit() > 0) {
                throw new SubscribeException(SECURITY_DEPOSIT_PRICE_INFO_NOT_FOUND);
            } else if (mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getPaidSecurityDeposit() == 0 || !order.getIsNewOrder()) {
                return;
            }
        }
        List<Integer> writtenOffETagPriceInfoIds = orderPriceInfoList.stream()
            .filter(opi -> PriceInfoDefinition.PriceInfoCategory.ETag == opi.getCategory() && Objects.equals(securityDepositPayment.getPaymentId(), opi.getPaymentId()))
            .map(OrderPriceInfo::getId).collect(Collectors.toList());
        List<ETagInfo> writtenOffETagInfos = etagInfoRepository.getByOrderPriceInfoIdIn(writtenOffETagPriceInfoIds);

        // TAPPAY_TSIB退款
        buildAccountReq.setInvoices(new ArrayList<>());
        buildAccountReq.setPaymentMethods(new ArrayList<>());

        PaymentMethodReq securityDeposit = createPaymentMethod(
            TypeEnum.REFUND,
            PaymentMethodCodeEnum.getPaymentMethodCodeEnum(securityDepositPayment.getCardNumber()),
            securityDepositPayment.getAmount(),
            // 財務與訂閱PM要求將 referenceKey 由 orderNo 改為 TransactionNumber
            securityDepositPayment.getTransactionNumber(),
            Collections.singletonList(createTransactionItem(TypeEnum.REFUND, TransactionItemCodeEnum.MARGIN, securityDepositPayment.getAmount()))
        );

        if (!isLegal) {
            buildAccountReq.getPaymentMethods().add(securityDeposit);
        }

        int writtenOffETagAmount = 0;
        if (isLegal && CollectionUtils.isNotEmpty(writtenOffETagInfos)) {
            writtenOffETagAmount = writtenOffETagInfos.stream()
                .mapToInt(ETagInfo::getPaidETagAmt)
                .sum();
        }

        // 部分退保證金
        boolean isPartialRefund = refundOrderPriceInfo != null && !Objects.equals(refundOrderPriceInfo.getAmount(), securityDepositPayment.getAmount());
        if (isPartialRefund) {
            // MARGIN收入付款
            int chargeAmount = securityDepositPayment.getAmount() - refundOrderPriceInfo.getAmount() - writtenOffETagAmount;
            List<TransactionItemReq> transactionItems = Collections.singletonList(createTransactionItem(CHARGE, TransactionItemCodeEnum.RENT, chargeAmount));

            buildAccountReq.getPaymentMethods().add(createPaymentMethod(CHARGE, PaymentMethodCodeEnum.MARGIN, chargeAmount, securityDepositPayment.getTransactionNumber(), transactionItems));

            // 更新保證金退款金額為取消訂單的金額(部分退款的情況)
            securityDeposit.setAmount(refundOrderPriceInfo.getAmount());
            // 同步更新交易項目中的退款金額
            securityDeposit.getTransactionItems().get(0).setAmount(refundOrderPriceInfo.getAmount());
        }

        /*
         * RENT_SUBSCRIBE收入付款6000,  MARGIN退款4000
         */
        buildAccountReq.setTransactionItems(new ArrayList<>());
        // 部分退保證金
        TransactionItemReq refundItem = createTransactionItem(TypeEnum.REFUND, TransactionItemCodeEnum.MARGIN, securityDepositPayment.getAmount());
        if (!isLegal) {
            buildAccountReq.getTransactionItems().add(refundItem);
        }

        if (isPartialRefund) {
            // MARGIN收入付款
            int chargeAmount = securityDepositPayment.getAmount() - refundOrderPriceInfo.getAmount() - writtenOffETagAmount;
            buildAccountReq.getTransactionItems().add(createTransactionItem(CHARGE, TransactionItemCodeEnum.RENT, chargeAmount));
            // 更新交易項目中的保證金退款金額,使其與取消訂單的退款金額一致
            refundItem.setAmount(refundOrderPriceInfo.getAmount());
        }
        List<Invoices> invoicesList = invoiceService.getInvoiceWithNewTransaction(order.getOrderNo()).stream()
            .filter(inv -> inv.getIsCheckout() != InvoiceDefine.InvCheckoutStatus.NONE_CHECKOUT.getStatus())
            .collect(Collectors.toList());
        List<InvoiceReq> invoices = generateCheckoutInvoiceReq(order, invoicesList, orderPriceInfoMap);
        buildAccountReq.setInvoices(invoices);

        // 執行法務作業且沖銷 etag 金額 > 0
        if (isLegal && CollectionUtils.isNotEmpty(writtenOffETagInfos)) {
            List<TransactionItemReq> transactionItems = buildAccountReq.getTransactionItems();
            transactionItems.add(createTransactionItem(CHARGE, TransactionItemCodeEnum.ETAG, writtenOffETagAmount));
            buildAccountReq.setTransactionItems(transactionItems);

            // 付款資訊
            List<PaymentInfo> paymentInfoList = getPaymentsByOrder(order.getOrderNo()).stream()
                .filter(p -> PayAuth == p.getPaymentCategory())
                .collect(Collectors.toList());
            Map<Integer, PaymentInfo> paymentInfoIdMap = paymentInfoList.stream()
                .collect(Collectors.toMap(PaymentInfo::getPaymentId, Function.identity()));
            processEtagPayments(writtenOffETagInfos, Collections.emptyMap(), paymentInfoIdMap, buildAccountReq.getPaymentMethods());
        }

        // 過濾金額為 0 的項目
        buildAccountReq.setPaymentMethods(buildAccountReq.getPaymentMethods().stream().filter(p -> p.getAmount() > 0).collect(Collectors.toList()));
        buildAccountReq.setTransactionItems(buildAccountReq.getTransactionItems().stream().filter(t -> t.getAmount() > 0).collect(Collectors.toList()));
        generateUnReconciliationPayments(buildAccountReq, order);
        // 執行立帳
        Result<Object> result = finServiceBusClient.checkout(HeaderDefine.Platform.SERVER, HeaderDefine.SystemKind.SUB, buildAccountReq);
        if (result.getStatusCode() != 0) {
            log.error("訂單取消立帳失敗，{}", result.getMessage());
            Map<String, Object> alert = new LinkedHashMap<>();
            alert.put("request", buildAccountReq);
            alert.put("response", result);
            mattermostServer.notify(String.format("%s,OrderNo:%s", CHECK_OUT_CANCEL_ORDER_FAIL.getMsg(), order.getOrderNo()), alert, null);
            throw new SubscribeException(CHECK_OUT_CANCEL_ORDER_FAIL);
        }

        try {
            List<Long> accountIds = accountRepository.getAccountsByOrderNo(order.getOrderNo()).stream()
                .map(Account::getId)
                .collect(Collectors.toList());
            accountDetailRepository.updateCheckout(accountIds);
            invoiceService.setCheckout(invoicesList);
        } catch (Exception e) {
            Map<String, Object> alert = new LinkedHashMap<>();
            alert.put("request", buildAccountReq);
            alert.put("exception", e.getMessage());
            mattermostServer.notify(String.format("%s,OrderNo:%s", "登打狀態異動失敗", order.getOrderNo()), alert, e);
        }

        log.info("立帳回傳資訊:{}", result);
    }

    /**
     * 取得未收支登打的付款
     */
    public List<PaymentInfo> getUnReconciliationPayments(String orderNo) {
        List<PaymentInfo> paymentInfoList = getPaymentsByOrder(orderNo);
        Set<String> tradeId = new HashSet<>();
        accountRepository.getAccountsByOrderNo(orderNo).stream().filter(account -> account.getAccountType() == AccountType.Credit)
            .forEach(account -> tradeId.add(account.getTradeId()));
        String securityDepositTradeId = paymentInfoList.stream().filter(p -> p.getPayFor() == SecurityDeposit).findAny().map(PaymentInfo::getTradeId).orElse("None");
        List<Integer> paymentIds =
            priceInfoService.getPriceInfosByOrder(orderNo).stream().filter(opi -> opi.getPaymentId() != null && !tradeId.contains(opi.getRecTradeId()) && opi.getCategory() != PriceInfoDefinition.PriceInfoCategory.SecurityDeposit)
                .map(OrderPriceInfo::getPaymentId).collect(Collectors.toList());
        // 濾掉保證金付款(可能有退款)
        return paymentInfoList.stream().filter(paymentInfo -> paymentIds.contains(paymentInfo.getPaymentId()) && !paymentInfo.getTradeId().equalsIgnoreCase(securityDepositTradeId)).collect(Collectors.toList());
    }

    /**
     * 生成未對帳要退款的付款
     */
    public void generateUnReconciliationPayments(BuildAccountReq buildAccountReq, Orders order) {
        List<PaymentInfo> paymentInfoList = getUnReconciliationPayments(order.getOrderNo());
        if (CollectionUtils.isNotEmpty(paymentInfoList)) {
            List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrder(order.getOrderNo());
            List<PaymentMethodReq> paymentMethodReqList = Optional.ofNullable(buildAccountReq.getPaymentMethods()).orElseGet(ArrayList::new);
            List<TransactionItemReq> transactionItemReqList = Optional.ofNullable(buildAccountReq.getTransactionItems()).orElseGet(ArrayList::new);
            paymentInfoList.forEach(paymentInfo -> {
                Map<Integer, OrderPriceInfo> orderPriceInfoMap = orderPriceInfoList.stream().filter(opi -> Objects.equals(opi.getPaymentId(), paymentInfo.getPaymentId())).collect(Collectors.toMap(OrderPriceInfo::getId, Function.identity()));
                boolean isIncludeETag = orderPriceInfoMap.values().stream().anyMatch(opi -> opi.getCategory() == PriceInfoDefinition.PriceInfoCategory.ETag);
                if (isIncludeETag) {
                    Integer totalAmt = orderPriceInfoMap.values().stream().mapToInt(OrderPriceInfo::getActualPrice).sum();
                    if (paymentInfo.getAmount().equals(totalAmt)) {
                        return;
                    }
                }
                Map<Integer, OrderPriceInfo> orderPriceInfoMapWithoutMerchandise = new HashMap<>();
                Map<Integer, OrderPriceInfo> orderPriceInfoMapWithMerchandise = new HashMap<>();
                orderPriceInfoMap.forEach((key, value) -> {
                    if (value.getCategory() == PriceInfoDefinition.PriceInfoCategory.Merchandise) {
                        orderPriceInfoMapWithMerchandise.put(key, value);
                    } else {
                        orderPriceInfoMapWithoutMerchandise.put(key, value);
                    }
                });
                List<TransactionItemReq> subtransactionItemReqList = createTransactionItems(order, orderPriceInfoMapWithoutMerchandise)
                    .stream().peek(transactionItemReq -> transactionItemReq.setCode(TransactionItemCodeEnum.TAPPAY_ADVANCE.name())).collect(Collectors.toList());
                if (!orderPriceInfoMapWithMerchandise.isEmpty()) {
                    subtransactionItemReqList.addAll(createTransactionItems(order, orderPriceInfoMapWithMerchandise)
                        .stream().peek(transactionItemReq -> transactionItemReq.setCode(TransactionItemCodeEnum.TAPPAY_ADVANCE.name())).collect(Collectors.toList()));
                }
                upSideDownTransactionType(subtransactionItemReqList);
                transactionItemReqList.addAll(subtransactionItemReqList);
                PaymentMethodReq paymentMethodReq =
                    createPaymentMethod(TypeEnum.REFUND, PaymentMethodCodeEnum.getPaymentMethodCodeEnum(paymentInfo.getCardNumber()), paymentInfo.getAmount(), paymentInfo.getTransactionNumber(), subtransactionItemReqList);
                paymentMethodReq.setTransactionItems(subtransactionItemReqList);
                paymentMethodReqList.add(paymentMethodReq);
                buildAccountReq.setPaymentMethods(paymentMethodReqList);
                buildAccountReq.setTransactionItems(transactionItemReqList);
            });
        }

    }
}
