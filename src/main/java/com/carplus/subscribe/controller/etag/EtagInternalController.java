package com.carplus.subscribe.controller.etag;

import carplus.common.response.CarPlusRestController;
import carplus.common.utils.DateUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.model.etag.EtagInfoResponse;
import com.carplus.subscribe.model.etag.EtagPdfExportRequest;
import com.carplus.subscribe.model.etag.EtagRequest;
import com.carplus.subscribe.service.ETagService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 遠通ETAG API")
public class EtagInternalController {

    @Autowired
    private ETagService eTagService;

    @Operation(summary = "Etag出車")
    @PostMapping("subscribe/etag/rent")
    public void etagRentCar(
        @RequestHeader(CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
        @Validated @RequestBody EtagRequest request) {
        eTagService.rentCar(request.getOrderNo(), memberId);
    }


    @Operation(summary = "Etag產生新的出車")
    @PostMapping("subscribe/etag/rentCarForNewStage")
    public void etagRentCarForNewStage(@Validated @RequestBody EtagRequest request) {
        eTagService.rentCarForNewStage(request.getOrderNo());
    }

    @Operation(summary = "Etag還車")
    @PostMapping("subscribe/etag/return")
    public void etagReturn(@Validated @RequestBody EtagRequest request) {
        eTagService.returnCar(request.getOrderNo());
    }

    @Operation(summary = "Etag結單")
    @PostMapping("subscribe/etag/close")
    public ETagInfo etagClose(@Validated @RequestBody EtagRequest request) {
        return eTagService.closeCar(request.getOrderNo());
    }

    @Operation(summary = "拿取所有EtagInfo紀錄")
    @GetMapping("subscribe/{orderNo}/etag")
    public List<EtagInfoResponse> etagInfos(@PathVariable("orderNo") String orderNo) {
        return eTagService.getETagIntoByOrderNo(orderNo);
    }

    @Operation(summary = "ETag 通行費明細 PDF 匯出")
    @GetMapping(value = "subscribe/etag/export/pdf", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public ResponseEntity<byte[]> exportEtagPdf(@RequestHeader(CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                                @Validated EtagPdfExportRequest request) {
        try {
            // 生成 PDF
            byte[] pdfBytes = eTagService.generateEtagPdf(request, memberId);

            // 生成檔案名稱
            String fileName = String.format("通行費明細表_%s.pdf", DateUtils.toDateString(new Date(), "yyyyMMdd", DateUtils.ZONE_TPE));

            // 設定回應標頭
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.set(HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename*=UTF-8''" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
            headers.setContentLength(pdfBytes.length);

            return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);

        } catch (Exception e) {
            throw new RuntimeException("ETag 通行費明細 PDF 匯出失敗: " + e.getMessage(), e);
        }
    }
}
