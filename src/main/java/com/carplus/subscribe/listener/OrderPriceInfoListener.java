package com.carplus.subscribe.listener;

import com.carplus.subscribe.aspects.PriceInfoTrackingContext;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.utils.PriceInfoUtils;
import org.hibernate.event.spi.PostInsertEvent;
import org.hibernate.event.spi.PostInsertEventListener;
import org.hibernate.event.spi.PostUpdateEvent;
import org.hibernate.event.spi.PostUpdateEventListener;
import org.hibernate.persister.entity.EntityPersister;
import org.springframework.stereotype.Component;

@Component
public class OrderPriceInfoListener implements PostInsertEventListener, PostUpdateEventListener {

    @Override
    public void onPostInsert(PostInsertEvent event) {
        Object entity = event.getEntity();
        if (!(entity instanceof OrderPriceInfo)) {
            return;
        }

        OrderPriceInfo priceInfo = (OrderPriceInfo) entity;

        String memberId = PriceInfoTrackingContext.getMemberId();
        if (memberId != null) {
            PriceInfoUtils.setUpdater(priceInfo, memberId);
        }
    }

    @Override
    public void onPostUpdate(PostUpdateEvent event) {
        Object entity = event.getEntity();
        if (!(entity instanceof OrderPriceInfo)) {
            return;
        }

        OrderPriceInfo priceInfo = (OrderPriceInfo) entity;

        String memberId = PriceInfoTrackingContext.getMemberId();
        if (memberId != null && PriceInfoUtils.hasPriceInfoChanged(event.getOldState(), event.getState(), event.getPersister())) {
            PriceInfoUtils.setUpdater(priceInfo, memberId);
        }
    }

    @Override
    public boolean requiresPostCommitHanding(EntityPersister persister) {
        return false;
    }
}

