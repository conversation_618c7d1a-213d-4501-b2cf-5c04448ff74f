package com.carplus.subscribe.event.car;

import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 長放車註銷事件
 */
@Getter
public class CarReturnEvent extends ApplicationEvent {

    private final String orderNo;

    private final CarBaseInfoSearchResponse carCrsInfo;

    private final String memberId;

    public CarReturnEvent(Object source, String orderNo, CarBaseInfoSearchResponse carCrsInfo, String memberId) {
        super(source);
        this.orderNo = orderNo;
        this.carCrsInfo = carCrsInfo;
        this.memberId = memberId;
    }
}
